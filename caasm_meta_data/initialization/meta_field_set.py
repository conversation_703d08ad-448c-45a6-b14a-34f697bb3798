import logging

import xlrd

from caasm_meta_data.initialization.base import MetaDataInitialization
from caasm_meta_data.manager import MetaDataManager
from caasm_service.entity.meta_model import MetaFieldType, MetaModelType
from caasm_service.runtime import meta_field_service, meta_model_service
from caasm_tool.util import hump2underline

log = logging.getLogger()


class BaseImport(object):
    _local_fields = ["create_time", "name"]

    name_col_end_index = 8
    name_col_start_index = 1
    friend_start_index = 5

    def __init__(self, file_path):
        self.file_path = file_path
        self.row_length = None
        self.col_length = None
        self._save_buffer_size = 100
        self._parse_rule_setting_mapper = {
            MetaFieldType.ENUM: self._parse_enum_rule_setting,
            MetaFieldType.RELATION: self._parse_relation_rule_setting,
        }

    def handle(self):
        request_model_data = self._parse_file()

        friend_names = request_model_data.get("friend_names")
        name = request_model_data.get("name")
        description = request_model_data.get("description")
        display_name = request_model_data.get("display_name", "")
        fields = request_model_data.get("fields") or []
        category = request_model_data.get("category")
        priority = request_model_data.get("priority")

        MetaDataManager.padding_field(display_name, name, fields)

        friends = self.find_friend(friend_names)

        meta_model = meta_model_service.get_meta_model(name=name, model_type=self.model_type)
        if not meta_model:
            meta_model_entry = meta_model_service.load_entity(
                name=name,
                type=self.model_type,
                init=False,
                friends=friends,
                display_name=display_name,
                description=description,
                internal=True,
                category=category,
                priority=priority,
            )
            save_model_res = meta_model_service.save(meta_model_entry)
            if not save_model_res.flag:
                raise ValueError(f"{name} 存储失败:{save_model_res.msg}")
            meta_model = meta_model_service.get_meta_model(model_id=save_model_res.inserted_id)
        else:
            meta_model.friends = friends
            meta_model.category = category
            meta_model.description = description
            meta_model.display_name = display_name
            meta_model.priority = priority
            meta_model_service.update(meta_model)

        meta_field_service.delete_meta_field(model_id=meta_model.id)
        local_fields = meta_field_service.find_meta_field(model_id=meta_model.id, fields=self._local_fields)

        local_field_mapper = {local_field.name: local_field.create_time for local_field in local_fields}

        fields_length = len(fields)
        for start_size in range(0, fields_length, self._save_buffer_size):
            tmp_data = fields[start_size : start_size + self._save_buffer_size]
            self._padding_data(
                tmp_data,
                local_field_mapper,
                model_id=meta_model.id,
            )
            res = meta_field_service.save_multi_direct(tmp_data)
            if not res.flag:
                raise ValueError(f"{name} 存储失败:{res.msg}")

    @property
    def model_type(self):
        raise NotImplementedError

    def find_friend(self, friend_names):
        return []

    @classmethod
    def _padding_data(cls, data, local_field_mapper, **kwargs):
        for info in data:
            info["create_time"] = local_field_mapper.get("create_time")
            info.update(kwargs)

    def _parse_file(self):
        fd = xlrd.open_workbook(self.file_path)
        sheet = fd.sheet_by_index(0)

        self.row_length = sheet.nrows
        self.col_length = sheet.ncols

        row_index, fields, friend_names = 0, [], []
        result = {"name": "", "friend_names": friend_names, "fields": fields}

        while row_index < self.row_length:
            if row_index in (0, 2):
                row_index += 1
                continue
            if row_index == 1:
                result["name"] = hump2underline(self.__get_value(sheet, row_index, 0))
                result["category"] = hump2underline(self.__get_value(sheet, row_index, 1))
                result["display_name"] = self.__get_value(sheet, row_index, 2)
                result["description"] = self.__get_value(sheet, row_index, 3)
                result["priority"] = int(float(self.__get_value(sheet, row_index, self.friend_start_index - 1)))

                for tmp_friend_col_index in range(self.friend_start_index, self.col_length):
                    friend = self.__get_value(sheet, row_index, tmp_friend_col_index)
                    if not friend:
                        continue
                    if friend == "cloud_instance":
                        dd = 1
                    friend_names.append(friend)

                row_index += 1
            else:
                tmp_field = self.__parse_field(sheet, row_index)
                if not tmp_field["name"]:
                    log.warning(f"Not find field name, row index is {row_index}, path is {self.file_path}")
                    row_index += 1

                row_index += 1
                if tmp_field["type"] in (MetaFieldType.OBJECT, MetaFieldType.LIST):
                    row_index = self._parse_children(sheet, row_index, self.name_col_start_index, tmp_field["children"])

                self._build_field_query(tmp_field)
                fields.append(tmp_field)
        return result

    def _parse_children(self, sheet, row_index, col_index, result):
        last_sub_field = None
        while True:
            if row_index >= self.row_length:
                return row_index
            if col_index >= self.col_length:
                return row_index

            sub_field = self.__parse_field(sheet, row_index, col_index + 1)
            if sub_field["name"] == "":
                prev_col_field = self.__parse_field(sheet, row_index, col_index - 1)
                next_col_field = self.__parse_field(sheet, row_index, col_index + 2)

                if prev_col_field["name"]:
                    next_row_field = self.__parse_field(sheet, row_index + 1, col_index + 1)
                    if next_row_field["name"]:
                        row_index += 1
                        result.append(next_row_field)
                    else:
                        return row_index
                elif next_col_field["name"]:
                    last_sub_field["children"].append(next_col_field)
                    row_index = self._parse_children(sheet, row_index + 1, col_index + 2, next_col_field["children"])
                else:
                    return row_index
            else:
                result.append(sub_field)
                last_sub_field = sub_field
                row_index += 1

    def __parse_field(self, sheet, row_index, name_index=1):
        if row_index >= self.row_length:
            return {"name": ""}

        if self.name_col_start_index <= name_index <= self.name_col_end_index:
            name = hump2underline(self.__get_value(sheet, row_index, name_index))
        else:
            name = ""

        if not name:
            return {"name": name}

        field_type = self.__get_value(sheet, row_index, self.name_col_end_index + 10)
        if field_type not in MetaFieldType._value2member_map_:
            raise ValueError(f"第{row_index}行字段类型无效")
        priority = self.__get_value(sheet, row_index, 0)
        if priority == "":
            priority = row_index

        field_setting = {}

        if field_type == MetaFieldType.LIST:
            show_field_define = self.__get_value(sheet, row_index, self.name_col_end_index + 4)
            unique_field_define = self.__get_value(sheet, row_index, self.name_col_end_index + 5)
            sort_field_define = self.__get_value(sheet, row_index, self.name_col_end_index + 6)

            unique_fields = unique_field_define.split("&&") if unique_field_define else []
            sort_fields = sort_field_define.split("&&") if sort_field_define else []
            show_fields = [show_field_define] if show_field_define else []
            sort_type = True

            field_setting = {
                "unique_fields": [hump2underline(i) for i in unique_fields],
                "sort_fields": [hump2underline(i) for i in sort_fields],
                "sort_type": sort_type,
                "show_fields": [hump2underline(i) for i in show_fields],
            }
        field = {
            "name": name,
            "required": self.__get_value(sheet, row_index, self.name_col_end_index + 1) == "是",
            "allow_null": self.__get_value(sheet, row_index, self.name_col_end_index + 2) == "否",
            "encrypt": self.__get_value(sheet, row_index, self.name_col_end_index + 7) == "是",
            "display_name": self.__get_value(sheet, row_index, self.name_col_end_index + 9),
            "type": field_type,
            "description": self.__get_value(sheet, row_index, self.name_col_end_index + 12),
            "default": None,
            "unique": False,
            "hidden": self.__get_value(sheet, row_index, self.name_col_end_index + 3) == "是",
            "children": [],
            "internal": True,
            "setting": field_setting,
            "priority": int(float(priority)),
            "full_text_search": self.__get_value(sheet, row_index, self.name_col_end_index + 8) == "是",
        }

        rule_content = self.__get_value(sheet, row_index, self.name_col_end_index + 11)
        field["rules"] = self._parse_rule_setting(field, rule_content)

        return field

    @classmethod
    def __get_value(cls, sheet, row_index, col_index):
        value = sheet.cell_value(row_index, col_index)
        return str(value).strip()

    @classmethod
    def _build_field_query(cls, field):
        query = []

        cls.__build_field_query_loop(field, query)
        field["query"] = "--".join(query)

    @classmethod
    def __build_field_query_loop(cls, field, result):
        display_name = field["display_name"]
        description = field["description"]

        if display_name and display_name not in result:
            result.append(display_name)
        if description and description not in result:
            result.append(description)

        children = field["children"]
        if not children:
            return

        for child in children:
            cls.__build_field_query_loop(child, result)

    def _parse_rule_setting(self, field, setting_define):
        parse_rule_method = self._parse_rule_setting_mapper.get(field["type"])
        result = []
        if not parse_rule_method:
            return result

        try:
            result = parse_rule_method(setting_define)
        except Exception as e:
            log.error(f"{self.file_path}:{field['display_name']}({field['name']})规则解析失败")
        return result

    @classmethod
    def _parse_enum_rule_setting(cls, setting_define):
        settings = setting_define.split("\n")

        setting = {}
        for tmp_setting in settings:
            if not tmp_setting:
                continue
            key, val = tmp_setting.split(":")
            setting[val.strip()] = int(key.strip())

        if not setting:
            raise ValueError("枚举类型必须定义选型")
        result = [{"name": "enum", "setting": setting}]

        return result

    @classmethod
    def _parse_relation_rule_setting(cls, setting_define):
        settings = setting_define.split("\n")

        setting = {}
        for tmp_setting in settings:
            if not tmp_setting:
                continue
            key, val = tmp_setting.split(":")
            if key not in {"rel_type", "category", "entry_type", "display_source", "biz_source", "match"}:
                raise ValueError("关系类型字段错误")
            setting[key.strip()] = val.strip()
        return [{"name": "relation", "setting": setting}]


class FieldSetImport(BaseImport):
    @property
    def model_type(self):
        return MetaModelType.FIELD_SET


class MetaFieldSetInit(MetaDataInitialization):
    @property
    def my_import(self):
        return FieldSetImport

    @property
    def sub_path(self):
        return "field_set"

    def execute_core(self, path):
        try:
            self.my_import(path).handle()
        except Exception as e:
            log.warning(f"SonPath({path}) execute failed:{e}")
