name: "microsoft_ad"
display_name: "AD"
description: "Active Directory 存储有关网络上对象的信息，并让管理员和用户可以更容易地使用这些信息。 Active Directory 使用结构化数据存储作为目录信息的逻辑层次组织的基础。"
type: "账户"
company: "microsoft"
logo: "logo.png"
version: "0.4"
priority: 1
properties:
  - "账户"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以ldap/ldaps/http/https开头的地址信息"
        setting:
          reg: '^((ldap|ldaps|http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: query
    type: string
    required: true
    display_name: "查询条件"
    description: "查询条件"
    validate_rules:
      - name: length
        error_hint: "查询条件必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

fetch_setting:
  type: disposable
  point: "microsoft_ad.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "microsoft_ad.fetch:auth"
  size: 200
  fetch_type_mapper:
    asset:
      - computer
    account:
      - account


merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }